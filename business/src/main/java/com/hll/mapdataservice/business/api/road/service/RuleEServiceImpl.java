package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.common.PathCalculator;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.constant.Const;
import com.hll.mapdataservice.common.entity.LinkE;
import com.hll.mapdataservice.common.entity.RuleE;
import com.hll.mapdataservice.common.entity.RuleM;
import com.hll.mapdataservice.common.mapper.RuleEMapper;
import com.hll.mapdataservice.common.mapper.RuleMMapper;
import com.hll.mapdataservice.common.service.IRuleEService;
import com.hll.mapdataservice.common.service.IRuleMService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

@Slf4j
@Service
public class RuleEServiceImpl extends ServiceImpl<RuleEMapper, RuleE> implements IRuleEService {

    @Resource
    private LinkEServiceImpl linkEService;
    @Resource
    private NodeEServiceImpl nodeEService;

    public boolean handleRuleInfo(String country) {

        String[] schemas = country.split(",");

        for (String schema : schemas) {
            String old = DynamicDataSourceContextHolder.peek();
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(schema, false));
            try {
                doHandleRuleInfo();
            } finally {
                DynamicDataSourceContextHolder.push(old);
            }
        }
        return true;

    }


    private void doHandleRuleInfo() {
        long handleNum = 0L;
        final int PAGE_SIZE = 5_000;

        while (true) {
            // 只查 rule_info 为空且未删除的数据
            List<RuleE> rules = this.lambdaQuery()
                    .ne(RuleE::getStatus, Const.STATUS_DELETED)
                    .isNull(RuleE::getRuleInfo).list();

            if (CollUtil.isEmpty(rules)) {
                break;
            }

            // 计算 ruleInfo
            rules.forEach(this::fillRuleInfo);

            // 分页更新，降低锁粒度
            if (this.updateBatchById(rules, PAGE_SIZE)) {
                handleNum += rules.size();
            }

        }

        log.info("current market={}, handle rule(rule_info) size={}",
                DynamicDataSourceContextHolder.peek(), handleNum);
    }

    /**
     * 根据 inlink/outlink 计算并设置 ruleInfo
     */
    private void fillRuleInfo(RuleE rule) {
        log.info("fill rule info, ruleId={}", rule.getRuleId());
        String inId = rule.getInlinkId();
        String outId = rule.getOutlinkId();

        // 车道掉头
        if (Objects.equals(inId, outId)) {
            rule.setRuleInfo(Const.RULE_INFO_TURN);
            return;
        }

        // 其余场景
        PathCalculator calculator = new PathCalculator(inId, outId);
        List<String> pathIds = calculator.ruleComplete();
        Map<String, LinkE> linkMap = calculator.getLinkMap();

        if (CollUtil.isEmpty(pathIds)) {
            LinkE inLink = linkEService.queryById(inId, "0,2,3");
            LinkE outLink = linkEService.queryById(outId, "0,2,3");
            // CollUtil.newArrayList(inLink.getHllSNid(),inLink.getHllENid())
            if (!CollUtil.intersection(CollUtil.newArrayList(inLink.getHllSNid(), inLink.getHllENid()), CollUtil.newArrayList(outLink.getHllSNid(), outLink.getHllENid())).isEmpty()) {
                pathIds = CollUtil.newArrayList(inId, outId);
            } else {
                rule.setRuleInfo(Const.RULE_INFO_UNKNOWN);
                return;
            }
        }
        // // 防御性校验（避免数组越界或空指针）
        // if (CollUtil.isEmpty(pathIds) || pathIds.size() < 2) {
        //     log.warn("path not found, in={}, out={}, ruleId={}", inId, outId, rule.getRuleId());
        //     // 设置为未知类型，避免无限循环
        //     rule.setRuleInfo(Const.RULE_INFO_UNKNOWN);
        //     return;
        // }

        LinkE inLink = linkMap.get(inId);
        LinkE inLinkNext = linkMap.get(pathIds.get(1));
        LinkE outLink = linkMap.get(outId);
        LinkE outLinkBefore = linkMap.get(pathIds.get(pathIds.size() - 2));

        int ruleInfo = 0;
        try {
            ruleInfo = this.ruleInfo(inLink, outLink, inLinkNext, outLinkBefore);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        if (rule.getFlag() == 3 && ruleInfo == Const.RULE_INFO_TURN) {
            rule.setRuleInfo(Const.RULE_INFO_ALLOW_TURN);
        } else {
            rule.setRuleInfo(ruleInfo);
        }
    }


    public int ruleInfo(LinkE inLink, LinkE outLink, LinkE inLinkNext, LinkE outLinkBefore) throws ParseException {
        double angle = calculateAngle(inLink, outLink, inLinkNext, outLinkBefore);
        int type;
        if (angle > 135 && angle <= 225) {
            type = Const.RULE_INFO_TURN;
        } else if (angle > 225 && angle <= 315) {
            type = Const.RULE_INFO_LEFT;
        } else if ((angle > 315 && angle <= 360) || (angle >= 0 && angle <= 45)) {
            type = Const.RULE_INFO_FORWARD;
        } else {
            type = Const.RULE_INFO_RIGHT;
        }
        return type;
    }


    public Double calculateAngle(LinkE inLink, LinkE outLink, LinkE inLinkNext, LinkE outLinkBefore) throws ParseException {
        WKTReader wktReader = new WKTReader();
        Geometry inLinkGeom = wktReader.read(inLink.getGeometryWkt());
        Geometry outLinkGeom = wktReader.read(outLink.getGeometryWkt());

        String inLinkDir = inLink.getDir();
        String outLinkDir = outLink.getDir();

        if (inLinkDir.equals(Const.LINK_DIR_UNKNOWN) || inLinkDir.equals(Const.LINK_DIR_DOUBLE)) {
            if (inLink.getHllSNid().equals(inLinkNext.getHllSNid()) || inLink.getHllSNid().equals(inLinkNext.getHllENid())) {
                inLinkGeom = inLinkGeom.reverse();
            }
        }
        if (outLinkDir.equals(Const.LINK_DIR_UNKNOWN) || outLinkDir.equals(Const.LINK_DIR_DOUBLE)) {
            if (outLink.getHllENid().equals(outLinkBefore.getHllSNid()) || outLink.getHllENid().equals(outLinkBefore.getHllENid())) {
                outLinkGeom = outLinkGeom.reverse();
            }
        }
        if (inLinkDir.equals(Const.LINK_DIR_BACKWARD)) {
            inLinkGeom = inLinkGeom.reverse();
        }

        if (outLinkDir.equals(Const.LINK_DIR_BACKWARD)) {
            outLinkGeom = outLinkGeom.reverse();
        }

        if (inLinkDir.equals(Const.LINK_DIR_DOUBLE_NO) || outLinkDir.equals(Const.LINK_DIR_DOUBLE_NO)) {
            return Double.MAX_VALUE;
        }

        Coordinate[] inLinkCoordinates = inLinkGeom.getCoordinates();
        Coordinate[] outLinkCoordinates = outLinkGeom.getCoordinates();

        Coordinate[] inLinkCoordinate = new Coordinate[]{inLinkCoordinates[inLinkCoordinates.length - 2], inLinkCoordinates[inLinkCoordinates.length - 1]};
        Coordinate[] outLinkCoordinate = new Coordinate[]{outLinkCoordinates[0], outLinkCoordinates[1]};

        double inLink_northBased_arc = this.azimuth(inLinkCoordinate[0], inLinkCoordinate[1]);
        double inLink_northBased_angle = inLink_northBased_arc * 180 / Math.PI;

        double outLink_northBased_arc = this.azimuth(outLinkCoordinate[0], outLinkCoordinate[1]);
        double outLink_northBased_angle = outLink_northBased_arc * 180 / Math.PI;
        double angle = outLink_northBased_angle - inLink_northBased_angle;
        if (angle < 0) {
            angle = 360 + angle;
        }
        return angle;
    }

    public double azimuth(Coordinate from, Coordinate to) {
        double dx = to.x - from.x;
        double dy = to.y - from.y;

        // 计算相对于正北方向的方位角（顺时针）
        double azimuth = Math.atan2(dx, dy);

        // 如果角度为负，则转换为正值（确保在 0 ~ 2π 范围内）
        if (azimuth < 0) {
            azimuth += 2 * Math.PI;
        }

        return azimuth;
    }
}
